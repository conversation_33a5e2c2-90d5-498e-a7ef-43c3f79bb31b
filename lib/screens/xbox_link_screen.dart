import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/xbox_auth_service.dart';
import '../widgets/common/gf_button.dart';
import '../widgets/xbox_icon.dart';

class XboxLinkScreen extends StatefulWidget {
  const XboxLinkScreen({super.key});

  @override
  State<XboxLinkScreen> createState() => _XboxLinkScreenState();
}

class _XboxLinkScreenState extends State<XboxLinkScreen> {
  bool _isLoading = false;
  bool _isLinked = false;
  String? _linkedGamertag;

  @override
  void initState() {
    super.initState();
    AppLogger.info('Xbox link screen - initializing');
    _checkXboxAccountStatus();
  }

  Future<void> _checkXboxAccountStatus() async {
    try {
      final isLinked = await XboxAuthService.instance.isXboxAccountLinked();
      final linkedGamertag = XboxAuthService.instance.linkedGamertag;

      if (mounted) {
        setState(() {
          _isLinked = isLinked;
          _linkedGamertag = linkedGamertag;
        });
      }
    } catch (e) {
      AppLogger.error('Error checking Xbox account status: $e');
    }
  }

  Future<void> _linkXboxAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await XboxAuthService.instance.startXboxOAuth();

      if (success) {
        // OAuth completed successfully, refresh the account status
        await _checkXboxAccountStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Xbox account linked successfully!'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Xbox account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _unlinkXboxAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await XboxAuthService.instance.unlinkXboxAccount();

      if (success) {
        if (mounted) {
          setState(() {
            _isLinked = false;
            _linkedGamertag = null;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Xbox account unlinked successfully!'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unlink Xbox account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text('Link Xbox Account'),
        backgroundColor: AppColors.gfDarkBackground,
        foregroundColor: AppColors.gfOffWhite,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),

              // Xbox logo/icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.gfCardBackground,
                  borderRadius: BorderRadius.circular(60),
                  border: Border.all(color: AppColors.gfGrayBorder, width: 2),
                ),
                child: const Center(
                  child: XboxIcon(size: 60, color: AppColors.gfGreen),
                ),
              ),
              const SizedBox(height: 32),

              // Title
              Text(
                _isLinked ? 'Xbox Account Linked' : 'Link Your Xbox Account',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Status or description
              if (_isLinked) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGreen, width: 1),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: AppColors.gfGreen,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Xbox Account Connected',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Gamertag: ${_linkedGamertag ?? 'Unknown'}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.gfGrayText,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'You can now browse and download your Xbox screenshots and game clips when creating posts.',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.gfGrayText,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Text(
                  'Connect your Xbox account to access your screenshots and game clips directly in GameFlex.',
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.gfGrayText,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // OAuth explanation
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.security,
                            color: AppColors.gfGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Secure Authentication',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.gfOffWhite,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'We\'ll open Xbox Live in your browser for secure authentication. Your login credentials are never shared with GameFlex.',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.gfGrayText,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 32),

              // Action buttons
              if (_isLinked) ...[
                // Unlink button
                SizedBox(
                  width: double.infinity,
                  child: GFButton(
                    text: _isLoading ? 'Unlinking...' : 'Unlink Xbox Account',
                    onPressed: _isLoading ? null : _unlinkXboxAccount,
                    type: GFButtonType.danger,
                    isEnabled: !_isLoading,
                  ),
                ),
              ] else ...[
                // Link button
                SizedBox(
                  width: double.infinity,
                  child: GFButton(
                    text: _isLoading ? 'Linking...' : 'Link Xbox Account',
                    onPressed: _isLoading ? null : _linkXboxAccount,
                    type: GFButtonType.primary,
                    isEnabled: !_isLoading,
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Back button
              SizedBox(
                width: double.infinity,
                child: GFButton(
                  text: 'Back',
                  onPressed: () => Navigator.of(context).pop(),
                  type: GFButtonType.secondary,
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
