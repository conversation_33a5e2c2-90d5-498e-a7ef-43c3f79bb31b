import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/xbox_auth_service.dart';
import 'image_picker_screen.dart';
import 'account_linking_screen.dart';
import 'xbox_media_browser_screen.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  bool _isLogoGreen = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      body: SafeArea(
        child: Column(
          children: [
            // Main content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Link Gaming Accounts Section
                    _buildLinkGamingAccountsSection(),

                    const SizedBox(height: 32),

                    // Linked Libraries Section
                    _buildLinkedLibrariesSection(),

                    const SizedBox(height: 32),

                    // Phone Library Section
                    _buildPhoneLibrarySection(),
                  ],
                ),
              ),
            ),

            // App Logo at bottom
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: _buildAppLogo(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkGamingAccountsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Link Gaming Accounts',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfOffWhite,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Connect your gaming accounts to access and share content from your game libraries. Upload your best gaming moments with ease.',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.gfGrayText,
            height: 1.4,
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPlatformCard('Xbox', Icons.videogame_asset, () async {
              await _handleXboxIntegration();
            }),
            _buildPlatformCard('PlayStation', Icons.sports_esports, () {
              _showComingSoonSnackBar('PlayStation integration');
            }),
            _buildPlatformCard('Steam', Icons.computer, () {
              _showComingSoonSnackBar('Steam integration');
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildPlatformCard(String name, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: AppColors.gfGrayText),
            const SizedBox(height: 4),
            Text(
              name,
              style: const TextStyle(
                fontSize: 10,
                color: AppColors.gfGrayText,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkedLibrariesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Linked Libraries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfOffWhite,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Access all your linked game libraries and screenshots.',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.gfGrayText,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneLibrarySection() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const ImagePickerScreen()),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground40,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.photo_library,
                color: AppColors.gfOffWhite,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Text(
                'Phone Library',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.gfOffWhite,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.gfGrayText,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppLogo() {
    return Center(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _isLogoGreen = !_isLogoGreen;
          });
        },
        child: SizedBox(
          width: 60,
          height: 60,
          child: ColorFiltered(
            colorFilter:
                _isLogoGreen
                    ? ColorFilter.mode(AppColors.gfGreen, BlendMode.srcIn)
                    : const ColorFilter.mode(
                      Colors.transparent,
                      BlendMode.multiply,
                    ),
            child: Image.asset(
              'assets/images/icons/icon_tpt.png',
              width: 60,
              height: 60,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  void _showComingSoonSnackBar(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _handleXboxIntegration() async {
    try {
      // Check if Xbox account is linked
      final isLinked = await XboxAuthService.instance.isXboxAccountLinked();

      if (!mounted) return;

      if (!isLinked) {
        // Show dialog asking if user wants to link Xbox account
        final shouldLink = await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                backgroundColor: AppColors.gfDarkBackground,
                title: const Text(
                  'Xbox Account Not Linked',
                  style: TextStyle(color: AppColors.gfOffWhite),
                ),
                content: const Text(
                  'You need to link your Xbox account to access your screenshots and game clips. Would you like to link it now?',
                  style: TextStyle(color: AppColors.gfGrayText),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(color: AppColors.gfGrayText),
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text(
                      'Link Account',
                      style: TextStyle(color: AppColors.gfGreen),
                    ),
                  ),
                ],
              ),
        );

        if (shouldLink == true && mounted) {
          // Navigate to account linking screen
          await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AccountLinkingScreen(),
            ),
          );

          // After linking, check again and proceed if successful
          final isNowLinked =
              await XboxAuthService.instance.isXboxAccountLinked();
          if (isNowLinked && mounted) {
            _openXboxMediaBrowser();
          }
        }
      } else {
        // Xbox account is linked, open media browser
        _openXboxMediaBrowser();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accessing Xbox integration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openXboxMediaBrowser() async {
    final downloadedFiles = await Navigator.of(context).push<List<File>>(
      MaterialPageRoute(builder: (context) => const XboxMediaBrowserScreen()),
    );

    if (downloadedFiles != null && downloadedFiles.isNotEmpty && mounted) {
      // Handle the downloaded files - navigate to image picker or editor
      // For now, just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Downloaded ${downloadedFiles.length} Xbox media files',
          ),
          backgroundColor: AppColors.gfGreen,
        ),
      );

      // TODO: Navigate to image editor with the first downloaded file
      // This would depend on your existing image editing flow
    }
  }
}
