import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';
import '../models/user_model.dart';

/// Xbox Sign In Response
class XboxSignInResponse {
  final bool success;
  final String message;
  final UserModel? user;
  final AuthTokens? tokens;
  final bool requiresUsername;

  XboxSignInResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
    this.requiresUsername = false,
  });
}

/// Auth Tokens
class AuthTokens {
  final String accessToken;
  final String refreshToken;
  final String idToken;

  AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.idToken,
  });
}

/// Xbox authentication service for linking Xbox accounts via OpenXBL OAuth
class XboxAuthService {
  static final XboxAuthService _instance = XboxAuthService._internal();
  static XboxAuthService get instance => _instance;
  XboxAuthService._internal();

  // Xbox account linking status
  bool _isLinking = false;
  String? _linkedGamertag;

  /// Get linking status
  bool get isLinking => _isLinking;
  String? get linkedGamertag => _linkedGamertag;

  /// Start Xbox OAuth authentication flow using flutter_web_auth_2
  Future<bool> startXboxOAuth() async {
    if (_isLinking) {
      developer.log('XboxAuthService: Already linking Xbox account');
      return false;
    }

    _isLinking = true;

    try {
      developer.log('XboxAuthService: Starting Xbox OAuth flow');
      AppLogger.info('Starting Xbox OAuth authentication');

      // Get the OAuth URL from the backend
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/auth',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        final authUrl = data['authUrl'] as String?;

        if (authUrl == null) {
          throw Exception('No auth URL received from server');
        }

        developer.log('XboxAuthService: Starting OAuth with URL: $authUrl');
        AppLogger.info('Starting Xbox OAuth with flutter_web_auth_2');

        // Use flutter_web_auth_2 to handle the OAuth flow
        final result = await FlutterWebAuth2.authenticate(
          url: authUrl,
          callbackUrlScheme: 'io.gameflex.oauth',
        );

        developer.log('XboxAuthService: OAuth completed with result: $result');
        AppLogger.info('Xbox OAuth completed');

        // Extract the authorization code from the callback URL
        final uri = Uri.parse(result);
        final code = uri.queryParameters['code'];

        if (code == null) {
          throw Exception('No authorization code received from OAuth callback');
        }

        // Complete the linking process with the authorization code
        final success = await _completeOAuthLinking(code);

        if (success) {
          developer.log('XboxAuthService: Xbox account linked successfully');
          AppLogger.info('Xbox account linked successfully');
          return true;
        } else {
          throw Exception('Failed to complete Xbox account linking');
        }
      } else {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage = data['error'] ?? 'Failed to start Xbox OAuth';
        developer.log(
          'XboxAuthService: Failed to start Xbox OAuth: $errorMessage',
        );
        AppLogger.error('Failed to start Xbox OAuth: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      developer.log('XboxAuthService: Error in Xbox OAuth: $e');
      AppLogger.error('Error in Xbox OAuth: $e');
      rethrow;
    } finally {
      _isLinking = false;
    }
  }

  /// Complete OAuth linking with authorization code
  Future<bool> _completeOAuthLinking(String authCode) async {
    try {
      developer.log('XboxAuthService: Completing OAuth linking with auth code');
      AppLogger.info('Completing Xbox OAuth linking');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/xbox/link',
        body: {'authCode': authCode},
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        _linkedGamertag = data['gamertag'];
        developer.log('XboxAuthService: OAuth linking completed successfully');
        AppLogger.info('Xbox OAuth linking completed successfully');
        return true;
      } else {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage =
            data['error'] ?? 'Failed to complete OAuth linking';
        developer.log(
          'XboxAuthService: Failed to complete OAuth linking: $errorMessage',
        );
        AppLogger.error('Failed to complete OAuth linking: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      developer.log('XboxAuthService: Error completing OAuth linking: $e');
      AppLogger.error('Error completing OAuth linking: $e');
      rethrow;
    }
  }

  /// Unlink Xbox account
  Future<bool> unlinkXboxAccount() async {
    try {
      developer.log('XboxAuthService: Unlinking Xbox account');
      AppLogger.info('Unlinking Xbox account');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/xbox/account',
      );

      if (response.statusCode == 200) {
        _linkedGamertag = null;
        developer.log('XboxAuthService: Successfully unlinked Xbox account');
        AppLogger.info('Successfully unlinked Xbox account');
        return true;
      } else {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage = data['error'] ?? 'Failed to unlink Xbox account';
        developer.log(
          'XboxAuthService: Failed to unlink Xbox account: $errorMessage',
        );
        AppLogger.error('Failed to unlink Xbox account: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      developer.log('XboxAuthService: Error unlinking Xbox account: $e');
      AppLogger.error('Error unlinking Xbox account: $e');
      rethrow;
    }
  }

  /// Check if Xbox account is linked
  Future<bool> isXboxAccountLinked() async {
    try {
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/account',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        // The /xbox/account endpoint returns the account details nested under 'xboxAccount'
        final xboxAccount = data['xboxAccount'];
        _linkedGamertag = xboxAccount?['gamertag'];
        return true;
      } else if (response.statusCode == 404) {
        // 404 means no Xbox account is linked
        _linkedGamertag = null;
        return false;
      }
      return false;
    } catch (e) {
      developer.log('XboxAuthService: Error checking Xbox account status: $e');
      AppLogger.error('Error checking Xbox account status: $e');
      return false;
    }
  }

  /// Get linked Xbox account information
  Future<Map<String, dynamic>?> getLinkedAccount() async {
    try {
      developer.log('XboxAuthService: Getting linked Xbox account');
      AppLogger.info('Getting linked Xbox account information');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/account',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxAuthService: Successfully retrieved Xbox account info',
        );
        AppLogger.info('Successfully retrieved Xbox account information');
        return data;
      } else if (response.statusCode == 404) {
        developer.log('XboxAuthService: No Xbox account linked');
        AppLogger.info('No Xbox account linked to user');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxAuthService: Failed to get Xbox account: ${data['error']}',
        );
        AppLogger.error('Failed to get Xbox account', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error getting Xbox account: $e');
      AppLogger.error('Error getting Xbox account', error: e);
      return null;
    }
  }

  /// Sign in with Xbox account
  Future<XboxSignInResponse> signInWithXbox() async {
    try {
      developer.log('XboxAuthService: Starting Xbox Sign In');
      AppLogger.auth('Starting Xbox Sign In');

      // Get the OAuth URL from the backend
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/xbox/auth',
      );

      if (response.statusCode != 200) {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage = data['error'] ?? 'Failed to get Xbox auth URL';
        return XboxSignInResponse(success: false, message: errorMessage);
      }

      final data = ApiService.instance.parseResponse(response);
      final authUrl = data['authUrl'] as String?;

      if (authUrl == null) {
        return XboxSignInResponse(
          success: false,
          message: 'No auth URL received from server',
        );
      }

      developer.log(
        'XboxAuthService: Starting Xbox Sign In OAuth with URL: $authUrl',
      );
      AppLogger.info('Starting Xbox Sign In OAuth with flutter_web_auth_2');

      // Use flutter_web_auth_2 to handle the OAuth flow
      final result = await FlutterWebAuth2.authenticate(
        url: authUrl,
        callbackUrlScheme: 'io.gameflex.oauth',
      );

      developer.log(
        'XboxAuthService: Xbox Sign In OAuth completed with result: $result',
      );
      AppLogger.info('Xbox Sign In OAuth completed');

      // Extract the authorization code from the callback URL
      final uri = Uri.parse(result);
      final code = uri.queryParameters['code'];

      if (code == null) {
        return XboxSignInResponse(
          success: false,
          message: 'No authorization code received from OAuth callback',
        );
      }

      // Complete the sign in process with the authorization code
      return await _completeXboxSignIn(code);
    } catch (e) {
      developer.log('XboxAuthService: Xbox Sign In failed: $e');
      AppLogger.error('Xbox Sign In failed', error: e);

      String errorMessage;
      if (e.toString().contains('User cancelled')) {
        errorMessage = 'Xbox Sign In was cancelled';
      } else {
        errorMessage = 'Xbox Sign In failed: $e';
      }

      return XboxSignInResponse(success: false, message: errorMessage);
    }
  }

  /// Complete Xbox sign in with authorization code
  Future<XboxSignInResponse> _completeXboxSignIn(String authCode) async {
    try {
      developer.log('XboxAuthService: Completing Xbox Sign In with auth code');
      AppLogger.info('Completing Xbox Sign In');

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/xbox/signin',
        body: {'authCode': authCode},
      );

      final data = ApiService.instance.parseResponse(response);

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Parse user data
        final userData = data['user'] as Map<String, dynamic>?;
        UserModel? user;
        if (userData != null) {
          user = UserModel.fromJson(userData);
        }

        // Parse tokens
        final tokensData = data['tokens'] as Map<String, dynamic>?;
        AuthTokens? tokens;
        if (tokensData != null) {
          final accessToken = tokensData['accessToken'] as String?;
          final refreshToken = tokensData['refreshToken'] as String?;
          final idToken = tokensData['idToken'] as String?;

          if (accessToken != null && refreshToken != null && idToken != null) {
            tokens = AuthTokens(
              accessToken: accessToken,
              refreshToken: refreshToken,
              idToken: idToken,
            );
          }
        }

        developer.log('XboxAuthService: Xbox Sign In successful');
        AppLogger.auth('Xbox Sign In successful');

        return XboxSignInResponse(
          success: true,
          message: data['message'] as String? ?? 'Xbox Sign In successful',
          user: user,
          tokens: tokens,
          requiresUsername: data['requiresUsername'] as bool? ?? false,
        );
      } else {
        final errorMessage = data['error'] ?? 'Failed to complete Xbox Sign In';
        developer.log(
          'XboxAuthService: Failed to complete Xbox Sign In: $errorMessage',
        );
        AppLogger.error('Failed to complete Xbox Sign In: $errorMessage');

        return XboxSignInResponse(success: false, message: errorMessage);
      }
    } catch (e) {
      developer.log('XboxAuthService: Error completing Xbox Sign In: $e');
      AppLogger.error('Error completing Xbox Sign In: $e');

      return XboxSignInResponse(
        success: false,
        message: 'Failed to complete Xbox Sign In: $e',
      );
    }
  }

  /// Legacy method for claiming Xbox auth (deprecated)
  /// This method is kept for backward compatibility with deep link service
  Future<bool> claimXboxAuth(String tempId) async {
    developer.log(
      'XboxAuthService: claimXboxAuth called with tempId: $tempId (deprecated)',
    );
    AppLogger.warning(
      'claimXboxAuth method is deprecated, use signInWithXbox instead',
    );
    return false;
  }
}
