import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import {
    CognitoIdentityProviderClient,
    AdminCreateUserCommand,
    AdminSetUserPasswordCommand,
    AdminInitiateAuthCommand,
    AdminCreateUserCommandInput,
    AdminInitiateAuthCommandInput,
    AuthFlowType
} from '@aws-sdk/client-cognito-identity-provider';
import { v4 as uuidv4 } from 'uuid';
const fetch = require('node-fetch');

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const secretsManager = new SecretsManagerClient(awsConfig);
const cognitoClient = new CognitoIdentityProviderClient(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;
const APP_CONFIG_SECRET_NAME = process.env.APP_CONFIG_SECRET_NAME;
const XBOX_CONFIG_SECRET_NAME = process.env.XBOX_CONFIG_SECRET_NAME;
const USER_POOL_ID = process.env.USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;

// Xbox Live API endpoints
const XBOX_AUTH_URL = 'https://user.auth.xboxlive.com/user/authenticate';
const XSTS_AUTH_URL = 'https://xsts.auth.xboxlive.com/xsts/authorize';
const MICROSOFT_TOKEN_URL = 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token';
const MICROSOFT_AUTH_URL = 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize';

// Xbox Live API endpoints for data
const XBOX_PROFILE_URL = 'https://profile.xboxlive.com/users/me/profile/settings';

let azureConfig: {
    clientId: string;
    clientSecret: string;
} | null = null;

// TypeScript interfaces
interface XboxLinkRequest {
    authCode: string; // OAuth authorization code for linking
}

interface XboxSignInRequest {
    authCode: string; // OAuth authorization code for sign in
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}



interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    profilePictureUrl?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    tokenExpiresAt: string;
}

// Xbox Live authentication response interfaces
interface XboxLiveAuthResponse {
    IssueInstant: string;
    NotAfter: string;
    Token: string;
    DisplayClaims: {
        xui: Array<{
            uhs: string;
        }>;
    };
}

// Microsoft OAuth token response
interface MicrosoftTokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    scope: string;
    refresh_token?: string;
}



// Helper function to get Azure config from secrets
const getAzureConfig = async (): Promise<typeof azureConfig> => {
    if (azureConfig) {
        return azureConfig;
    }

    // Use Xbox config secret first, then fall back to app config secret
    let secretName = XBOX_CONFIG_SECRET_NAME;
    let useXboxSecret = true;

    if (!secretName) {
        secretName = APP_CONFIG_SECRET_NAME;
        useXboxSecret = false;
    }

    if (!secretName) {
        throw new Error('Neither XBOX_CONFIG_SECRET_NAME nor APP_CONFIG_SECRET_NAME environment variable is set');
    }

    try {
        const command = new GetSecretValueCommand({
            SecretId: secretName,
        });
        const response = await secretsManager.send(command);

        if (!response.SecretString) {
            throw new Error('Secret value is empty');
        }

        const secrets = JSON.parse(response.SecretString);

        azureConfig = {
            clientId: secrets.azureClientId,
            clientSecret: secrets.azureClientSecret
        };

        if (!azureConfig.clientId || !azureConfig.clientSecret) {
            throw new Error(`Azure client ID or secret not found in ${useXboxSecret ? 'Xbox config' : 'app config'} secret`);
        }

        return azureConfig;
    } catch (error) {
        console.error('Error getting Azure config:', error);
        throw new Error(`Failed to retrieve Azure configuration from ${secretName}: ${(error as Error).message}`);
    }
};

// Helper function to exchange Microsoft access token for Xbox Live tokens
const getXboxLiveTokens = async (accessToken: string): Promise<{ xstsToken: string; userHash: string; expiresAt: string }> => {
    try {
        // Step 1: Authenticate with Xbox Live using Microsoft access token
        const xboxAuthResponse = await fetch(XBOX_AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-xbl-contract-version': '1'
            },
            body: JSON.stringify({
                Properties: {
                    AuthMethod: 'RPS',
                    SiteName: 'user.auth.xboxlive.com',
                    RpsTicket: `d=${accessToken}`
                },
                RelyingParty: 'http://auth.xboxlive.com',
                TokenType: 'JWT'
            })
        });

        if (!xboxAuthResponse.ok) {
            throw new Error(`Xbox Live authentication failed: ${xboxAuthResponse.status}`);
        }

        const xboxAuthData: XboxLiveAuthResponse = await xboxAuthResponse.json();

        // Step 2: Get XSTS token for Xbox Live API access
        const xstsResponse = await fetch(XSTS_AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-xbl-contract-version': '1'
            },
            body: JSON.stringify({
                Properties: {
                    SandboxId: 'RETAIL',
                    UserTokens: [xboxAuthData.Token]
                },
                RelyingParty: 'http://xboxlive.com',
                TokenType: 'JWT'
            })
        });

        if (!xstsResponse.ok) {
            throw new Error(`XSTS authentication failed: ${xstsResponse.status}`);
        }

        const xstsData: XboxLiveAuthResponse = await xstsResponse.json();

        return {
            xstsToken: xstsData.Token,
            userHash: xstsData.DisplayClaims.xui[0].uhs,
            expiresAt: xstsData.NotAfter
        };
    } catch (error) {
        console.error('Error getting Xbox Live tokens:', error);
        throw error;
    }
};

// Helper function to get Xbox profile using XSTS token
const getXboxProfile = async (xstsToken: string, userHash: string): Promise<any> => {
    try {
        const response = await fetch(`${XBOX_PROFILE_URL}?settings=GameDisplayName,GameDisplayPicRaw,Gamertag`, {
            headers: {
                'Authorization': `XBL3.0 x=${userHash};${xstsToken}`,
                'x-xbl-contract-version': '2'
            }
        });

        if (!response.ok) {
            throw new Error(`Xbox profile request failed: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error getting Xbox profile:', error);
        throw error;
    }
};

// Link Xbox account to user
const linkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('LinkXboxAccount: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        let parsedBody: XboxLinkRequest;
        try {
            parsedBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error('LinkXboxAccount: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { authCode } = parsedBody;

        if (!authCode) {
            return createResponse(400, {
                error: 'Missing required field: authCode'
            });
        }

        try {
            // Get Azure configuration
            const config = await getAzureConfig();
            if (!config) {
                return createResponse(500, { error: 'Azure configuration not available' });
            }

            // Exchange authorization code for Microsoft access token
            const tokenResponse = await fetch(MICROSOFT_TOKEN_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    client_id: config.clientId,
                    client_secret: config.clientSecret,
                    code: authCode,
                    grant_type: 'authorization_code',
                    redirect_uri: `https://${event.headers.Host}/xbox/callback`
                })
            });

            if (!tokenResponse.ok) {
                throw new Error(`Failed to exchange authorization code: ${tokenResponse.status}`);
            }

            const tokenData: MicrosoftTokenResponse = await tokenResponse.json();

            // Get Xbox Live tokens using Microsoft access token
            const xboxTokens = await getXboxLiveTokens(tokenData.access_token);

            // Get Xbox profile
            const xboxProfile = await getXboxProfile(xboxTokens.xstsToken, xboxTokens.userHash);

            // Extract gamertag from profile
            const gamertag = xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'Gamertag')?.value;
            if (!gamertag) {
                throw new Error('Could not retrieve gamertag from Xbox profile');
            }

            // Extract Xbox User ID (XUID)
            const xuid = xboxProfile.profileUsers[0].id;

            // Check if Xbox account is already linked to another user
            const existingXboxAccount = await checkExistingXboxAccount(xuid);
            if (existingXboxAccount && existingXboxAccount.userId !== userId) {
                return createResponse(409, {
                    error: 'This Xbox account is already linked to another user'
                });
            }

            const xboxAccountId = uuidv4();
            const now = new Date().toISOString();

            // Create Xbox account record
            const xboxAccount: XboxAccount = {
                id: xboxAccountId,
                userId,
                xboxUserId: xuid,
                gamertag: gamertag,
                xstsToken: xboxTokens.xstsToken,
                userHash: xboxTokens.userHash,
                profilePictureUrl: xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'GameDisplayPicRaw')?.value,
                isActive: true,
                createdAt: now,
                updatedAt: now,
                tokenExpiresAt: xboxTokens.expiresAt
            };

            // Store Xbox account in database
            await dynamodb.send(new PutCommand({
                TableName: XBOX_ACCOUNTS_TABLE,
                Item: xboxAccount
            }));

            return createResponse(200, {
                message: 'Xbox account linked successfully',
                xboxAccount: {
                    id: xboxAccount.id,
                    gamertag: xboxAccount.gamertag,
                    profilePictureUrl: xboxAccount.profilePictureUrl
                }
            });

        } catch (error) {
            console.error('LinkXboxAccount: Error:', error);
            return createResponse(500, {
                error: 'Failed to link Xbox account',
                details: (error as Error).message
            });
        }
    } catch (error) {
        console.error('LinkXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to link Xbox account',
            details: (error as Error).message
        });
    }
};



// Helper function to authenticate user with Cognito (for Xbox sign in)
const authenticateUserWithCognito = async (email: string, cognitoUserId: string): Promise<any> => {
    try {
        // For Xbox Sign In users, we need to use admin authentication
        // since we don't have their password. We'll set a temporary password
        // and then authenticate with it.
        const tempPassword = `TempXbox${Math.random().toString(36).substring(2)}!A1`;

        // Set the password for the user
        const setPasswordParams = {
            UserPoolId: USER_POOL_ID,
            Username: cognitoUserId,
            Password: tempPassword,
            Permanent: true
        };

        const setPasswordCommand = new AdminSetUserPasswordCommand(setPasswordParams);
        await cognitoClient.send(setPasswordCommand);

        // Now authenticate with the temporary password
        const authParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH' as AuthFlowType,
            AuthParameters: {
                USERNAME: cognitoUserId,
                PASSWORD: tempPassword
            }
        };

        const authCommand = new AdminInitiateAuthCommand(authParams);
        const authResult = await cognitoClient.send(authCommand);

        return authResult.AuthenticationResult;
    } catch (error) {
        console.error('Error authenticating Xbox user with Cognito:', error);
        return null;
    }
};

// Helper function to check existing Xbox account
const checkExistingXboxAccount = async (xuid: string): Promise<XboxAccount | null> => {
    try {
        if (!XBOX_ACCOUNTS_TABLE) {
            throw new Error('XBOX_ACCOUNTS_TABLE not configured');
        }

        const queryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'XboxUserIdIndex',
            KeyConditionExpression: 'xboxUserId = :xuid',
            ExpressionAttributeValues: {
                ':xuid': xuid
            }
        };

        const result = await dynamodb.send(new QueryCommand(queryParams));

        if (result.Items && result.Items.length > 0) {
            return result.Items[0] as XboxAccount;
        }

        return null;
    } catch (error) {
        console.error('Error checking existing Xbox account:', error);
        return null;
    }
};

// Unlink Xbox account
const unlinkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Update user record to remove Xbox information
        const updateUserParams = {
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'REMOVE xboxUserId, xboxGamertag SET updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':updatedAt': new Date().toISOString()
            }
        };
        await dynamodb.send(new UpdateCommand(updateUserParams));

        return createResponse(200, {
            message: 'Xbox account unlinked successfully'
        });

    } catch (error) {
        console.error('UnlinkXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to unlink Xbox account',
            details: (error as Error).message
        });
    }
};

// Xbox Sign In - authenticate with Xbox account (create new account or login to existing)
const xboxSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('XboxSignIn: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        let parsedBody: XboxSignInRequest;
        try {
            parsedBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error('XboxSignIn: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { authCode } = parsedBody;

        if (!authCode) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        // Get Azure configuration
        const config = await getAzureConfig();
        if (!config) {
            return createResponse(500, { error: 'Xbox configuration not available' });
        }

        // Exchange authorization code for Microsoft access token
        const tokenParams = new URLSearchParams({
            client_id: config.clientId,
            client_secret: config.clientSecret,
            code: authCode,
            grant_type: 'authorization_code',
            redirect_uri: `https://${event.headers.Host}/xbox/callback`
        });

        const tokenResponse = await fetch(MICROSOFT_TOKEN_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenParams.toString()
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            console.error('XboxSignIn: Microsoft token exchange failed:', errorText);
            return createResponse(400, { error: 'Failed to exchange authorization code for access token' });
        }

        const tokenData: MicrosoftTokenResponse = await tokenResponse.json();

        // Get Xbox Live tokens using Microsoft access token
        const xboxTokens = await getXboxLiveTokens(tokenData.access_token);

        // Get Xbox profile
        const xboxProfile = await getXboxProfile(xboxTokens.xstsToken, xboxTokens.userHash);

        // Extract Xbox user information
        const gamertag = xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'Gamertag')?.value;
        if (!gamertag) {
            throw new Error('Could not retrieve gamertag from Xbox profile');
        }

        const xuid = xboxProfile.profileUsers[0].id;
        if (!xuid) {
            throw new Error('Could not retrieve Xbox User ID from profile');
        }

        // Check if Xbox account already exists (linked to any user)
        const existingXboxAccount = await checkExistingXboxAccount(xuid);

        if (existingXboxAccount) {
            // Xbox account exists - sign in to existing user account
            console.log('XboxSignIn: Existing Xbox account found, signing in user:', existingXboxAccount.userId);

            // Get the user record
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: existingXboxAccount.userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (!userResult.Item) {
                return createResponse(404, { error: 'User account not found for linked Xbox account' });
            }

            const user = userResult.Item as UserRecord;

            // Update Xbox account tokens
            const updateXboxParams = {
                TableName: XBOX_ACCOUNTS_TABLE,
                Key: { id: existingXboxAccount.id },
                UpdateExpression: 'SET xstsToken = :xstsToken, userHash = :userHash, tokenExpiresAt = :tokenExpiresAt, updatedAt = :updatedAt',
                ExpressionAttributeValues: {
                    ':xstsToken': xboxTokens.xstsToken,
                    ':userHash': xboxTokens.userHash,
                    ':tokenExpiresAt': xboxTokens.expiresAt,
                    ':updatedAt': new Date().toISOString()
                }
            };
            await dynamodb.send(new UpdateCommand(updateXboxParams));

            // Generate Cognito tokens for the user
            const authResult = await authenticateUserWithCognito(user.email, user.cognitoUserId);

            if (!authResult) {
                return createResponse(500, { error: 'Failed to generate authentication tokens' });
            }

            // Check if username is required
            const requiresUsername = !user.username || user.username.trim() === '';

            return createResponse(200, {
                message: 'Xbox Sign In successful',
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt
                },
                tokens: {
                    accessToken: authResult.AccessToken,
                    refreshToken: authResult.RefreshToken,
                    idToken: authResult.IdToken
                },
                requiresUsername
            });

        } else {
            // Xbox account doesn't exist - create new user account
            console.log('XboxSignIn: No existing Xbox account found, creating new user account');

            // Create a new user account with Xbox information
            const userId = uuidv4();
            const now = new Date().toISOString();

            // Generate a unique email for Xbox users (they can change it later)
            const xboxEmail = `xbox_${xuid}@gameflex.temp`;

            // Create user in Cognito with temporary password
            const tempPassword = `XboxTemp${Math.random().toString(36).substring(2)}!A1`;

            const cognitoParams: AdminCreateUserCommandInput = {
                UserPoolId: USER_POOL_ID,
                Username: xboxEmail,
                TemporaryPassword: tempPassword,
                MessageAction: 'SUPPRESS',
                UserAttributes: [
                    { Name: 'email', Value: xboxEmail },
                    { Name: 'email_verified', Value: 'true' },
                    { Name: 'given_name', Value: gamertag },
                    { Name: 'family_name', Value: '' }
                ]
            };

            const createUserCommand = new AdminCreateUserCommand(cognitoParams);
            const cognitoUser = await cognitoClient.send(createUserCommand);

            // Set permanent password
            const setPasswordCommand = new AdminSetUserPasswordCommand({
                UserPoolId: USER_POOL_ID,
                Username: xboxEmail,
                Password: tempPassword,
                Permanent: true
            });
            await cognitoClient.send(setPasswordCommand);

            // Create user record in DynamoDB
            const userRecord: UserRecord = {
                id: userId,
                email: xboxEmail,
                firstName: gamertag,
                lastName: '',
                cognitoUserId: cognitoUser.User?.Username || xboxEmail,
                createdAt: now,
                updatedAt: now
            };

            const putUserCommand = new PutCommand({
                TableName: USERS_TABLE,
                Item: userRecord
            });
            await dynamodb.send(putUserCommand);

            // Create Xbox account record
            const xboxAccountId = uuidv4();
            const xboxAccount: XboxAccount = {
                id: xboxAccountId,
                userId,
                xboxUserId: xuid,
                gamertag: gamertag,
                xstsToken: xboxTokens.xstsToken,
                userHash: xboxTokens.userHash,
                profilePictureUrl: xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'GameDisplayPicRaw')?.value,
                isActive: true,
                createdAt: now,
                updatedAt: now,
                tokenExpiresAt: xboxTokens.expiresAt
            };

            const putXboxCommand = new PutCommand({
                TableName: XBOX_ACCOUNTS_TABLE,
                Item: xboxAccount
            });
            await dynamodb.send(putXboxCommand);

            // Generate Cognito tokens for the new user
            const authResult = await authenticateUserWithCognito(xboxEmail, cognitoUser.User?.Username || xboxEmail);

            if (!authResult) {
                return createResponse(500, { error: 'Failed to generate authentication tokens' });
            }

            return createResponse(201, {
                message: 'Xbox Sign In successful - new account created',
                user: {
                    id: userId,
                    email: xboxEmail,
                    username: null, // Username will be required
                    firstName: gamertag,
                    lastName: '',
                    createdAt: now,
                    updatedAt: now
                },
                tokens: {
                    accessToken: authResult.AccessToken,
                    refreshToken: authResult.RefreshToken,
                    idToken: authResult.IdToken
                },
                requiresUsername: true // New Xbox accounts always require username setup
            });
        }

    } catch (error) {
        console.error('XboxSignIn error:', error);
        return createResponse(500, {
            error: 'Failed to sign in with Xbox',
            details: (error as Error).message
        });
    }
};

// Get user's linked Xbox account
const getLinkedXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const queryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'UserIdIndex', // GSI on userId
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        };

        const result = await dynamodb.send(new QueryCommand(queryParams));

        if (!result.Items || result.Items.length === 0) {
            return createResponse(404, { error: 'No Xbox account linked' });
        }

        const xboxAccount = result.Items[0] as XboxAccount;

        return createResponse(200, {
            xboxAccount: {
                id: xboxAccount.id,
                gamertag: xboxAccount.gamertag,
                xboxUserId: xboxAccount.xboxUserId,
                profilePictureUrl: xboxAccount.profilePictureUrl,
                linkedAt: xboxAccount.createdAt
            }
        });

    } catch (error) {
        console.error('GetLinkedXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to get Xbox account',
            details: (error as Error).message
        });
    }
};

// Handler for starting Xbox authentication flow
const startXboxAuth = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        // For Xbox sign in, userId can be null (unauthenticated users)
        // For Xbox linking, userId is required (authenticated users)

        const config = await getAzureConfig();

        if (!config) {
            return createResponse(500, { error: 'Azure configuration not available' });
        }

        if (!config.clientId) {
            return createResponse(500, { error: 'Azure client ID not configured' });
        }

        // Generate the Microsoft OAuth URL
        const redirectUri = `https://${event.headers.Host}/xbox/callback`;
        const scopes = 'XboxLive.signin XboxLive.offline_access';

        // Use userId as state if available (for linking), otherwise use 'signin' (for sign in)
        const state = userId || 'signin';

        const authUrl = `${MICROSOFT_AUTH_URL}?` + new URLSearchParams({
            client_id: config.clientId,
            response_type: 'code',
            redirect_uri: redirectUri,
            scope: scopes,
            state: state
        }).toString();

        return createResponse(200, {
            authUrl,
            message: 'Redirect user to this URL to authenticate with Xbox Live'
        });
    } catch (error) {
        console.error('StartXboxAuth error:', error);
        return createResponse(500, {
            error: 'Failed to start Xbox authentication',
            details: (error as Error).message
        });
    }
};

// Handler for Microsoft OAuth callback
const handleXboxCallback = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('Xbox callback received:', JSON.stringify(event.queryStringParameters, null, 2));

        const { code, state } = event.queryStringParameters || {};

        if (!code) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        if (!state) {
            return createResponse(400, { error: 'State parameter (user ID) is required' });
        }

        // Return a simple success page that will trigger the frontend to complete the linking
        const successHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Xbox Account Linking</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .success { color: #4CAF50; }
                    .loading { color: #2196F3; }
                </style>
            </head>
            <body>
                <h1 class="success">✓ Xbox Authentication Successful</h1>
                <p class="loading">Completing account linking...</p>
                <script>
                    // Post message to parent window (mobile app webview)
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'xbox_auth_success',
                            code: '${code}',
                            state: '${state}'
                        }));
                    }
                    // For web browsers, redirect to a custom URL scheme
                    else {
                        window.location.href = 'io.gameflex.oauth://xbox/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}';
                    }
                </script>
            </body>
            </html>
        `;

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*'
            },
            body: successHtml
        };

    } catch (error) {
        console.error('HandleXboxCallback error:', error);
        return createResponse(500, {
            error: 'Failed to handle Xbox callback',
            details: (error as Error).message
        });
    }
};



// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Xbox handler received event:', JSON.stringify(event, null, 2));

    const httpMethod = event.httpMethod;
    const resource = event.resource;

    try {
        if (httpMethod === 'GET' && resource === '/xbox/auth') {
            return await startXboxAuth(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/callback') {
            return await handleXboxCallback(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/link') {
            return await linkXboxAccount(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/signin') {
            return await xboxSignIn(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/account') {
            return await getLinkedXboxAccount(event);
        } else if (httpMethod === 'DELETE' && resource === '/xbox/account') {
            return await unlinkXboxAccount(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Xbox handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
